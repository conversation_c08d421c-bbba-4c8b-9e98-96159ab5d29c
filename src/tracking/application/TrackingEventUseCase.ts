import {
  left,
  right,
  UniqueEntityID,
} from '@discocil/fv-domain-library/domain';

import { contextualizeError } from '@/cross-cutting/domain/decorators/ContextualizeErrorDecorator';
import { MicrositeCriteriaMother } from '@/microsite/domain/filters/MicrositeCriteriaMother';
import { MissingCredentialsError } from '@/tracking/domain/errors/MissingCredentialsError';

import { EventMetaFactory } from '../domain/services/EventTrackingFactory';

import type { InternalMessageBrokerClient } from '@/cross-cutting/domain/messageBroker/InternalMessageBrokerClientContracts';
import type { MicrositeRepository } from '@/microsite/domain/contracts/MicrositeRepository';
import type { TrackingEventDto, TrackingEventEither } from '@/tracking/domain/contracts/TrackingEventUseCaseContracts';
import type { Credentials, TrackingTrackRepository } from '@/tracking/domain/contracts/TrackingRepository';
import type { UseCase } from '@discocil/fv-domain-library/application';

export class TrackingEventUseCase implements UseCase<TrackingEventDto, Promise<TrackingEventEither>> {
  constructor(
    private readonly trackingRepository: TrackingTrackRepository,
    private readonly micrositeRepository: MicrositeRepository,
    private readonly internalMessageBroker: InternalMessageBrokerClient,
  ) {}

  @contextualizeError()
  async execute(dto: TrackingEventDto): Promise<TrackingEventEither> {
    const micrositeCriteria = MicrositeCriteriaMother.organizationToMatch(UniqueEntityID.build(dto.organizationId));
    const micrositeOrError = await this.micrositeRepository.find(micrositeCriteria);

    if (micrositeOrError.isLeft()) {
      return left(micrositeOrError.value);
    }

    const microsite = micrositeOrError.value;

    if (!microsite.hasFacebookCredentials()) {
      const exceptionError = MissingCredentialsError.build({
        context: this.constructor.name,
        data: { dto },
      });

      exceptionError.contextualize({ context: this.constructor.name, data: { dto } });

      return left(exceptionError);
    }

    const eventFactoryOrError = EventMetaFactory.execute(dto);

    if (eventFactoryOrError.isLeft()) {
      return left(eventFactoryOrError.value);
    }

    const event = eventFactoryOrError.value;

    const credentials: Credentials = {
      accessToken: microsite.facebookAccessToken.get(),
      pixelId: microsite.facebookPixelId.get(),
    };

    const successOrError = await this.trackingRepository.track(event, credentials);

    if (successOrError.isLeft()) {
      return left(successOrError.value);
    }

    this.internalMessageBroker.publish(event.pullDomainEvents());

    return right(true);
  }
}
