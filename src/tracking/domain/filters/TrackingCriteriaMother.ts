import {
  Criteria,
  Filters,
  Order,
} from '@discocil/fv-criteria-converter-library/domain';
import { type UniqueEntityID } from '@discocil/fv-domain-library/domain';

import { IdFilter } from '@/cross-cutting/domain/filters/IdFilter';

import type { TrackingKeys } from '../services/EventTrackingFactory';

export class TrackingCriteriaMother {
  private static readonly defaultOrderKey: TrackingKeys = 'createdAt';

  static idToMatch(id: UniqueEntityID): Criteria {
    const filters = Filters.build();

    filters.add(IdFilter.buildEqual(id));
    // filters.add(RemovedAtFilter.buildActive());
    // filters.add(ActiveFilter.buildTrue());

    return Criteria.build(
      filters,
      Order.asc<TrackingKeys>(
        this.defaultOrderKey,
      ),
    );
  }
}
