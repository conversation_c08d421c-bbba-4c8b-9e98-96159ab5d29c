import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { EventTracking } from '../entities/EventTracking';
import type { TrackingsEither } from '../services/EventTrackingFactory';

export interface TrackingSaveRepository {
  save: (event: EventTracking) => Promise<void>;
}
export interface TrackingSearchRepository {
  search: (criteria: Criteria) => Promise<TrackingsEither>;
}
