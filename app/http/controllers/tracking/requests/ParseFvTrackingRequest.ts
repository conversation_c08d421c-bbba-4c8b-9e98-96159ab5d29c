import { FvDate, Maybe } from '@discocil/fv-domain-library/domain';
import { singleton } from 'tsyringe';

import { FingerPrint } from '@/cross-cutting/infrastructure/middlewares/fingerPrint/contracts/FingerPrint';
import { EEventChannel, EventType } from '@/tracking/domain/value-objects/EventType';
import {
  FvAddToCartRequest, FvInitiateCheckoutRequest, FvPurchaseRequest,
} from '@app/http/@types/cli-api/tracking/fvBodySchema';

import { TrackingEventFingerPrint } from './ParseTrackingRequest';

import type {
  TrackingContentPrimitives,
  TrackingItemPrimitive,
} from '@/tracking/domain/contracts/EntityContracts';
import type { TrackingEventContentDto, TrackingEventDto } from '@/tracking/domain/contracts/TrackingEventUseCaseContracts';
import type { AddToCartPrimitives } from '@/tracking/domain/entities/AddToCart';
import type { InitiateCheckoutPrimitives } from '@/tracking/domain/entities/InitiateCheckout';
import type { PurchasePrimitives } from '@/tracking/domain/entities/Purchase';
import type { FvTrackingRequest } from '@app/http/@types/cli-api/tracking/schema';
import type {
  ECurrency,
  MoneyProps,
} from '@discocil/fv-domain-library/domain';

type TrackingFvEventPrimitives = AddToCartPrimitives | InitiateCheckoutPrimitives | PurchasePrimitives;

@singleton()
export class ParseFvTrackingRequest {
  static parser(request: FvTrackingRequest): TrackingEventDto {
    const eventTypeOrError = EventType.build(request.body.event_name);

    if (eventTypeOrError.isLeft()) {
      throw eventTypeOrError.value;
    }

    const eventType = eventTypeOrError.value;

    const eventDto = this.buildEvent(eventType, request);

    const trackingEventDto: TrackingEventDto = {
      channel: request.params.channel ?? EEventChannel.meta,
      ...eventDto,
      externalId: eventDto.externalId.map(item => item),
      user: eventDto.user.map(item => item),
      fb: eventDto.fb.map(item => item),
      route: 'route' in eventDto ? Maybe.some(eventDto.route as string) : Maybe.none<string>(),
      content: 'content' in eventDto ? Maybe.some(eventDto.content) : Maybe.none<TrackingEventContentDto>(),
      items: 'items' in eventDto ? Maybe.some(eventDto.items) : Maybe.none<TrackingItemPrimitive[]>(),
      price: 'price' in eventDto ? Maybe.some(eventDto.price) : Maybe.none<MoneyProps>(),
      numItems: 'numItems' in eventDto ? Maybe.some(eventDto.numItems) : Maybe.none<number>(),
      totalPrice: 'totalPrice' in eventDto ? Maybe.some(eventDto.totalPrice) : Maybe.none<MoneyProps>(),
      eventId: eventDto.eventId.map(item => item),
      sessionId: eventDto.sessionId.map(item => item),
      serviceType: eventDto.serviceType.map(item => item),
      containerType: eventDto.containerType.map(item => item),
    };

    return trackingEventDto;
  };

  private static buildEvent(eventType: EventType, request: FvTrackingRequest): TrackingFvEventPrimitives {
    const fingerPrint = this.parseFingerPrintRequest(request.fingerPrint.fingerPrint);

    if (eventType.isAddToCart()) {
      return this.parseAddToCartRequest(request.body as FvAddToCartRequest, fingerPrint);
    }

    if (eventType.isInitiateCheckout()) {
      return this.parseInitiateCheckoutRequest(request.body as FvInitiateCheckoutRequest, fingerPrint);
    }

    return this.parsePurchaseRequest(request.body as FvPurchaseRequest, fingerPrint);
  }

  private static parseMoneyRequest(amount: number, currency: string): MoneyProps {
    return {
      amount,
      currency: currency as ECurrency,
    };
  }

  private static parseFingerPrintRequest(fingerPrint: FingerPrint): TrackingEventFingerPrint {
    return {
      remoteAddress: fingerPrint.ip,
      userAgent: fingerPrint.browser.source,
    };
  }

  private static makeDefaultContent(): TrackingContentPrimitives {
    return {
      id: '',
      type: '',
      name: '',
      ids: [],
      date: FvDate.createFromSeconds(0).toPrimitive(),
    };
  }

  private static parseAddToCartRequest(bodyRequest: FvAddToCartRequest, fingerPrint: TrackingEventFingerPrint): AddToCartPrimitives {
    return {
      name: EventType.AddToCart().toPrimitive(),
      id: bodyRequest.event_id,
      organizationId: bodyRequest.organization_id,
      urlPage: bodyRequest.url_page,
      price: this.parseMoneyRequest(bodyRequest.price, bodyRequest.currency),
      eventId: Maybe.fromValue(bodyRequest.event_id),
      sessionId: Maybe.fromValue(bodyRequest.session_id),
      serviceType: Maybe.fromValue(bodyRequest.service_type),
      containerType: Maybe.fromValue(bodyRequest.container_type),
      ...fingerPrint,
      items: [],
      numItems: 0,
      totalPrice: this.parseMoneyRequest(0, bodyRequest.currency),
      user: Maybe.none(),
      fb: Maybe.none(),
      externalId: Maybe.none(),
      content: this.makeDefaultContent(),
    };
  }

  private static parseInitiateCheckoutRequest(
    bodyRequest: FvInitiateCheckoutRequest,
    fingerPrint: TrackingEventFingerPrint,
  ): InitiateCheckoutPrimitives {
    return {
      name: EventType.InitiateCheckout().toPrimitive(),
      id: bodyRequest.event_id,
      organizationId: bodyRequest.organization_id,
      urlPage: bodyRequest.url_page,
      price: this.parseMoneyRequest(bodyRequest.price, bodyRequest.currency),
      eventId: Maybe.fromValue(bodyRequest.event_id),
      sessionId: Maybe.fromValue(bodyRequest.session_id),
      serviceType: Maybe.fromValue(bodyRequest.service_type),
      containerType: Maybe.fromValue(bodyRequest.container_type),
      ...fingerPrint,
      items: [],
      numItems: 0,
      totalPrice: this.parseMoneyRequest(0, bodyRequest.currency),
      user: Maybe.none(),
      fb: Maybe.none(),
      externalId: Maybe.none(),
      content: this.makeDefaultContent(),
    };
  }

  private static parsePurchaseRequest(bodyRequest: FvPurchaseRequest, fingerPrint: TrackingEventFingerPrint): PurchasePrimitives {
    return {
      name: EventType.InitiateCheckout().toPrimitive(),
      id: bodyRequest.event_id,
      organizationId: bodyRequest.organization_id,
      urlPage: bodyRequest.url_page,
      price: this.parseMoneyRequest(bodyRequest.price, bodyRequest.currency),
      eventId: Maybe.fromValue(bodyRequest.event_id),
      sessionId: Maybe.fromValue(bodyRequest.session_id),
      serviceType: Maybe.fromValue(bodyRequest.service_type),
      containerType: Maybe.fromValue(bodyRequest.container_type),
      ...fingerPrint,
      items: [],
      numItems: 0,
      totalPrice: this.parseMoneyRequest(0, bodyRequest.currency),
      user: Maybe.none(),
      fb: Maybe.none(),
      externalId: Maybe.none(),
      content: this.makeDefaultContent(),
    };
  }
}
